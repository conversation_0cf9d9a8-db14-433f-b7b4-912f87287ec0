/* Header Localiza Style JavaScript */

odoo.define('theme_xtream.header', function (require) {
    'use strict';

    var publicWidget = require('web.public.widget');
    var core = require('web.core');

    publicWidget.registry.LocalizaHeader = publicWidget.Widget.extend({
        selector: '.o_header_standard',
        events: {
            'click .search_icon': '_onSearchToggle',
            'click .search_close': '_onSearchClose',
            'click .search_overlay': '_onSearchOverlayClick',
            'keydown .search_input': '_onSearchKeydown',
        },

        /**
         * @override
         */
        start: function () {
            this._super.apply(this, arguments);
            this._initStickyHeader();
            this._initMobileMenu();
            return this._super.apply(this, arguments);
        },

        /**
         * Initialize sticky header functionality
         */
        _initStickyHeader: function () {
            var self = this;
            var header = this.$el;
            var headerOffset = header.offset().top;

            $(window).scroll(function () {
                var scrollTop = $(window).scrollTop();
                
                if (scrollTop > headerOffset + 100) {
                    header.addClass('sticky');
                } else {
                    header.removeClass('sticky');
                }
            });
        },

        /**
         * Initialize mobile menu functionality
         */
        _initMobileMenu: function () {
            // Close mobile menu when clicking outside
            $(document).on('click', function (e) {
                if (!$(e.target).closest('.navbar-collapse, .navbar-toggler').length) {
                    $('.navbar-collapse').removeClass('show');
                }
            });

            // Smooth scroll for anchor links
            $('a[href^="#"]').on('click', function (e) {
                var target = $(this.getAttribute('href'));
                if (target.length) {
                    e.preventDefault();
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 100
                    }, 1000);
                }
            });
        },

        /**
         * Handle search toggle
         */
        _onSearchToggle: function (e) {
            e.preventDefault();
            $('.search_overlay').addClass('active').fadeIn(300);
            setTimeout(function () {
                $('.search_input').focus();
            }, 300);
        },

        /**
         * Handle search close
         */
        _onSearchClose: function (e) {
            e.preventDefault();
            $('.search_overlay').removeClass('active').fadeOut(300);
        },

        /**
         * Handle search overlay click (close when clicking outside)
         */
        _onSearchOverlayClick: function (e) {
            if (e.target === e.currentTarget) {
                this._onSearchClose(e);
            }
        },

        /**
         * Handle search input keydown
         */
        _onSearchKeydown: function (e) {
            if (e.keyCode === 27) { // ESC key
                this._onSearchClose(e);
            }
        },
    });

    // Additional header enhancements
    $(document).ready(function () {
        
        // Add loading animation to cart
        $('.o_wsale_my_cart').on('click', function () {
            var $this = $(this);
            $this.addClass('loading');
            setTimeout(function () {
                $this.removeClass('loading');
            }, 1000);
        });

        // Smooth hover effects for navigation
        $('.navbar-nav .nav-link').hover(
            function () {
                $(this).addClass('hovered');
            },
            function () {
                $(this).removeClass('hovered');
            }
        );

        // Add active class to current page navigation
        var currentPath = window.location.pathname;
        $('.navbar-nav .nav-link').each(function () {
            var linkPath = $(this).attr('href');
            if (currentPath === linkPath || (linkPath !== '/' && currentPath.indexOf(linkPath) === 0)) {
                $(this).addClass('active');
            }
        });

        // Top navigation active state
        $('.localiza_top_nav .nav-link').each(function () {
            var linkPath = $(this).attr('href');
            if (currentPath === linkPath || (linkPath !== '/' && currentPath.indexOf(linkPath) === 0)) {
                $(this).addClass('active');
            }
        });

        // Add scroll effect to top navigation
        $(window).scroll(function () {
            var scrollTop = $(window).scrollTop();
            if (scrollTop > 50) {
                $('.localiza_top_nav').addClass('scrolled');
            } else {
                $('.localiza_top_nav').removeClass('scrolled');
            }
        });

        // Mobile menu improvements
        $('.navbar-toggler').on('click', function () {
            $(this).toggleClass('active');
        });

        // Add ripple effect to buttons
        $('.nav-link, .search_icon, .user_link').on('click', function (e) {
            var $this = $(this);
            var ripple = $('<span class="ripple"></span>');
            var offset = $this.offset();
            var x = e.pageX - offset.left;
            var y = e.pageY - offset.top;
            
            ripple.css({
                left: x,
                top: y
            });
            
            $this.append(ripple);
            
            setTimeout(function () {
                ripple.remove();
            }, 600);
        });
    });

    return publicWidget.registry.LocalizaHeader;
});
