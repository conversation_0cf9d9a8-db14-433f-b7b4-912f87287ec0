# Header Localiza Style - Theme Xtream

## Visão Geral

Este documento descreve as customizações realizadas no header do tema Xtream para criar um design inspirado no site da Localiza, mantendo um esquema de cores azuis consistente.

## Características Implementadas

### 🎨 Design Visual
- **Barra de navegação superior**: Similar às abas da Localiza (Loja, Sobre Nós, Serviços, Contato)
- **Header principal**: Layout limpo com logo à esquerda, navegação central e ações do usuário à direita
- **Esquema de cores azuis**: Gradientes e tons de azul inspirados na identidade visual
- **Efeitos visuais**: Hover effects, animações suaves e transições

### 📱 Responsividade
- **Design mobile-first**: Adaptação completa para dispositivos móveis
- **Menu hambúrguer**: Navegação colapsável em telas menores
- **Touch-friendly**: Botões e links otimizados para toque

### ⚡ Funcionalidades Interativas
- **Header fixo**: Comportamento sticky ao fazer scroll
- **Busca overlay**: Modal de busca com animação
- **Efeitos ripple**: Feedback visual nos cliques
- **Animações**: Transições suaves e micro-interações

## Arquivos Modificados

### 1. Template HTML
**Arquivo**: `addons/theme_xtream/views/header_templates.xml`
- Estrutura completa do header inspirada na Localiza
- Navegação superior e principal
- Menu mobile responsivo
- Overlay de busca

### 2. Estilos CSS/SCSS
**Arquivo**: `addons/theme_xtream/static/src/scss/layout/_header.scss`
- Estilos para barra de navegação superior
- Design do header principal
- Responsividade e media queries
- Animações e efeitos visuais

### 3. Variáveis de Cores
**Arquivo**: `addons/theme_xtream/static/src/scss/_variables.scss`
- Esquema de cores azuis atualizado
- Cores primárias e secundárias
- Tons para hover e estados ativos

### 4. JavaScript
**Arquivo**: `addons/theme_xtream/static/src/js/header.js`
- Funcionalidade de header fixo
- Controle do overlay de busca
- Efeitos interativos
- Menu mobile

### 5. Assets e Layout
**Arquivo**: `addons/theme_xtream/views/layout_templates.xml`
- Inclusão dos novos assets
- Fontes Google Fonts
- Estilos inline para efeitos especiais

## Paleta de Cores

```scss
$color-brand: #1a237e;        // Azul principal profundo
$color-brand2: #484bd1;       // Azul primário
$color-brand3: #3f51b5;       // Azul secundário
$color-blue-light: #e3f2fd;   // Azul claro para fundos
$color-blue-dark: #0d47a1;    // Azul escuro para acentos
$color-button: #2196f3;       // Azul para botões
$color-hover: #1976d2;        // Azul para hover
```

## Estrutura do Header

### Barra Superior
```html
<div class="localiza_top_nav">
  <!-- Links de navegação secundária -->
</div>
```

### Header Principal
```html
<div class="localiza_main_header">
  <div class="container">
    <div class="row">
      <!-- Logo -->
      <div class="col-lg-3">...</div>
      <!-- Navegação -->
      <div class="col-lg-6">...</div>
      <!-- Ações do usuário -->
      <div class="col-lg-3">...</div>
    </div>
  </div>
</div>
```

## Funcionalidades JavaScript

### Header Fixo
- Ativa quando o usuário faz scroll
- Oculta a barra superior para economizar espaço
- Adiciona sombra para destacar

### Overlay de Busca
- Modal centralizado com animação fade
- Foco automático no campo de busca
- Fechamento com ESC ou clique fora

### Efeitos Visuais
- Ripple effect nos cliques
- Animações de loading
- Transições suaves

## Como Usar

1. **Instalação**: O tema já inclui todas as modificações
2. **Personalização**: Edite as variáveis em `_variables.scss` para ajustar cores
3. **Conteúdo**: Atualize os links de navegação conforme necessário
4. **Logo**: Substitua o logo padrão pela imagem da empresa

## Compatibilidade

- ✅ Odoo 18.0
- ✅ Navegadores modernos (Chrome, Firefox, Safari, Edge)
- ✅ Dispositivos móveis e tablets
- ✅ Temas responsivos

## Manutenção

### Atualizações de Cores
Para alterar o esquema de cores, edite o arquivo `_variables.scss`:

```scss
// Exemplo: mudança para tons de verde
$color-brand: #2e7d32;
$color-brand2: #4caf50;
```

### Adição de Links
Para adicionar novos links na navegação, edite o template `header_templates.xml`:

```xml
<li class="nav-item">
    <a class="nav-link" href="/novo-link">Novo Item</a>
</li>
```

## Suporte

Para dúvidas ou problemas relacionados a esta customização, consulte:
- Documentação do tema Xtream
- Guias de desenvolvimento Odoo
- Referências de CSS/SCSS e JavaScript

---

**Desenvolvido com base no design da Localiza**  
*Mantendo a identidade visual em tons de azul*
