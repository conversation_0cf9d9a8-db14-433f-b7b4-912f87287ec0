<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Assets for Localiza Header Style -->
    <template id="xtream_assets_frontend" inherit_id="website.assets_frontend" name="Xtream Frontend Assets">
        <xpath expr="." position="inside">
            <link rel="stylesheet" type="text/scss" href="/theme_xtream/static/src/scss/layout/_header.scss"/>
            <script type="text/javascript" src="/theme_xtream/static/src/js/header.js"></script>
        </xpath>
    </template>

    <template id="xtream_custom_layout" inherit_id="website.layout" name="Custom Layout">
        <!-- Add fonts and meta tags -->
        <xpath expr="//head" position="inside">
            <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital@1&amp;display=swap" rel="stylesheet"/>
            <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
            <meta name="theme-color" content="#484bd1"/>
        </xpath>

        <!-- Add custom CSS for ripple effect -->
        <xpath expr="//head" position="inside">
            <style>
                .ripple {
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(72, 75, 209, 0.3);
                    transform: scale(0);
                    animation: ripple-animation 0.6s linear;
                    pointer-events: none;
                }

                @keyframes ripple-animation {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }

                .loading {
                    position: relative;
                    overflow: hidden;
                }

                .loading::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
                    animation: loading-animation 1s ease-in-out;
                }

                @keyframes loading-animation {
                    0% { left: -100%; }
                    100% { left: 100%; }
                }

                .localiza_top_nav.scrolled {
                    background: linear-gradient(135deg, #3a3fd9 0%, #484bd1 100%);
                    transition: background 0.3s ease;
                }
            </style>
        </xpath>
    </template>
</odoo>
