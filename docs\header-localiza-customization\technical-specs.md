# Especificações Técnicas - Header Localiza Style

## Arquitetura da Solução

### Componentes Principais

1. **Template Engine**: Odoo QWeb Templates
2. **Styling**: SCSS com variáveis customizadas
3. **Interatividade**: JavaScript ES6+ com jQuery
4. **Framework CSS**: Bootstrap 5 (integrado ao Odoo)

## Estrutura de Arquivos

```
addons/theme_xtream/
├── views/
│   ├── header_templates.xml      # Templates do header
│   └── layout_templates.xml      # Assets e layout base
├── static/src/
│   ├── scss/
│   │   ├── _variables.scss       # Variáveis de cores e fontes
│   │   └── layout/_header.scss   # Estilos do header
│   └── js/
│       └── header.js            # Funcionalidades JavaScript
└── __manifest__.py              # Configuração do módulo
```

## Detalhes Técnicos

### Templates QWeb

#### Header Principal
```xml
<template id="xtream_header_localiza" inherit_id="website.layout">
    <xpath expr="//header" position="replace">
        <!-- Estrutura completa do header -->
    </xpath>
</template>
```

**Características:**
- Herda do layout padrão do Odoo
- Substitui completamente o header existente
- Mantém compatibilidade com funcionalidades do e-commerce

#### Estrutura de Navegação
```xml
<!-- Barra superior -->
<div class="localiza_top_nav">
    <ul class="nav nav-pills justify-content-center">
        <!-- Links de navegação -->
    </ul>
</div>

<!-- Header principal -->
<div class="localiza_main_header">
    <!-- Logo, navegação e ações do usuário -->
</div>
```

### Estilos SCSS

#### Variáveis Customizadas
```scss
// Cores principais
$color-brand: #1a237e;
$color-brand2: #484bd1;
$color-brand3: #3f51b5;

// Tipografia
$font-default: 'Poppins', sans-serif;
$font-offer: 'Roboto', sans-serif;
```

#### Mixins e Funções
```scss
// Gradiente azul personalizado
@mixin blue-gradient {
    background: linear-gradient(135deg, $color-brand2 0%, #3a3fd9 100%);
}

// Efeito hover padrão
@mixin hover-effect {
    transition: all 0.3s ease;
    &:hover {
        transform: translateY(-2px);
        color: $color-brand2;
    }
}
```

### JavaScript Modular

#### Widget Principal
```javascript
odoo.define('theme_xtream.header', function (require) {
    'use strict';
    
    var publicWidget = require('web.public.widget');
    
    publicWidget.registry.LocalizaHeader = publicWidget.Widget.extend({
        selector: '.o_header_standard',
        events: {
            'click .search_icon': '_onSearchToggle',
            'click .search_close': '_onSearchClose',
        },
        // Métodos de funcionalidade
    });
});
```

#### Funcionalidades Implementadas

1. **Header Fixo (Sticky)**
   ```javascript
   _initStickyHeader: function () {
       $(window).scroll(function () {
           var scrollTop = $(window).scrollTop();
           if (scrollTop > headerOffset + 100) {
               header.addClass('sticky');
           }
       });
   }
   ```

2. **Overlay de Busca**
   ```javascript
   _onSearchToggle: function (e) {
       $('.search_overlay').addClass('active').fadeIn(300);
       $('.search_input').focus();
   }
   ```

3. **Efeitos Visuais**
   ```javascript
   // Ripple effect
   $('.nav-link').on('click', function (e) {
       var ripple = $('<span class="ripple"></span>');
       // Posicionamento e animação
   });
   ```

## Performance e Otimização

### CSS
- **Minificação**: Automática via Odoo
- **Compressão GZIP**: Habilitada no servidor
- **Critical CSS**: Estilos essenciais inline
- **Lazy Loading**: Fontes carregadas de forma assíncrona

### JavaScript
- **Debouncing**: Eventos de scroll otimizados
- **Event Delegation**: Listeners eficientes
- **Memory Management**: Cleanup adequado de eventos

### Métricas de Performance
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1
- **Time to Interactive**: < 3.5s

## Responsividade

### Breakpoints
```scss
// Mobile First Approach
@media (max-width: 768px) {
    .localiza_main_header {
        padding: 10px 0;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    // Tablet styles
}

@media (min-width: 1025px) {
    // Desktop styles
}
```

### Estratégias Mobile
1. **Menu Hambúrguer**: Navegação colapsável
2. **Touch Targets**: Mínimo 44px de área tocável
3. **Swipe Gestures**: Suporte para gestos nativos
4. **Viewport Optimization**: Meta tags adequadas

## Acessibilidade (WCAG 2.1)

### Implementações
- **Contraste**: Mínimo 4.5:1 para texto normal
- **Navegação por teclado**: Tab order lógico
- **Screen Readers**: ARIA labels apropriados
- **Focus Indicators**: Visíveis e consistentes

### Código de Exemplo
```html
<button class="navbar-toggler" 
        aria-controls="mobileMenu" 
        aria-expanded="false" 
        aria-label="Toggle navigation">
    <span class="navbar-toggler-icon">
        <i class="fa fa-bars"></i>
    </span>
</button>
```

## Integração com Odoo

### Compatibilidade
- **Website Builder**: Totalmente compatível
- **E-commerce**: Carrinho e checkout funcionais
- **Multi-idioma**: Suporte a i18n
- **Multi-empresa**: Logos dinâmicos

### Hooks do Sistema
```python
# Exemplo de extensão Python se necessário
def _get_header_data(self):
    return {
        'company_logo': self.env.company.logo,
        'cart_quantity': self._get_cart_quantity(),
    }
```

## Testes e Validação

### Testes Automatizados
1. **Unit Tests**: Funções JavaScript isoladas
2. **Integration Tests**: Interação entre componentes
3. **E2E Tests**: Fluxos completos do usuário

### Validação Manual
- **Cross-browser**: Chrome, Firefox, Safari, Edge
- **Device Testing**: iOS, Android, Desktop
- **Accessibility**: Screen readers, keyboard navigation

## Deployment e Manutenção

### Processo de Deploy
1. **Backup**: Tema anterior
2. **Update**: Arquivos modificados
3. **Assets Rebuild**: Compilação SCSS
4. **Cache Clear**: Limpeza de cache
5. **Testing**: Validação pós-deploy

### Monitoramento
- **Error Tracking**: JavaScript errors
- **Performance Monitoring**: Core Web Vitals
- **User Analytics**: Comportamento de navegação

## Troubleshooting

### Problemas Comuns

1. **Estilos não aplicados**
   - Verificar compilação SCSS
   - Limpar cache do navegador
   - Validar ordem de carregamento CSS

2. **JavaScript não funciona**
   - Verificar console de erros
   - Confirmar carregamento de dependências
   - Validar seletores DOM

3. **Layout quebrado em mobile**
   - Testar breakpoints
   - Verificar viewport meta tag
   - Validar flexbox/grid

### Logs e Debug
```javascript
// Debug mode
if (odoo.debug) {
    console.log('Header initialized:', this.$el);
}
```

## Versionamento

### Controle de Versões
- **Semantic Versioning**: MAJOR.MINOR.PATCH
- **Git Tags**: Para releases
- **Changelog**: Documentação de mudanças

### Compatibilidade
- **Odoo 18.0**: Versão atual
- **Backward Compatibility**: Odoo 17.0+
- **Forward Compatibility**: Preparado para 19.0
