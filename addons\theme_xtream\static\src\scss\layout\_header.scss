/* Header Localiza Style with Blue Colors */

/* Top Navigation Bar */
.localiza_top_nav {
    background: linear-gradient(135deg, $color-brand2 0%, #3a3fd9 100%);
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .nav-pills {
        .nav-item {
            margin: 0 5px;

            .nav-link {
                color: rgba(255, 255, 255, 0.9);
                font-size: 13px;
                font-weight: 500;
                padding: 6px 16px;
                border-radius: 20px;
                transition: all 0.3s ease;
                text-decoration: none;

                &:hover {
                    background: rgba(255, 255, 255, 0.15);
                    color: $color-white;
                    transform: translateY(-1px);
                }

                &.active {
                    background: rgba(255, 255, 255, 0.2);
                    color: $color-white;
                }
            }
        }
    }
}

/* Main Header */
.localiza_main_header {
    background: $color-white;
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    position: relative;
    z-index: 1000;

    .navbar-brand {
        .logo img {
            max-height: 45px;
            width: auto;
            transition: transform 0.3s ease;

            &:hover {
                transform: scale(1.05);
            }
        }
    }

    /* Main Navigation */
    .navbar-nav {
        .nav-link {
            color: $color-brand;
            font-weight: 600;
            font-size: 15px;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 25px;
            transition: all 0.3s ease;
            position: relative;
            text-decoration: none;

            &:hover {
                color: $color-brand2;
                background: rgba(72, 75, 209, 0.08);
                transform: translateY(-2px);
            }

            &:before {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                width: 0;
                height: 2px;
                background: $color-brand2;
                transition: all 0.3s ease;
                transform: translateX(-50%);
            }

            &:hover:before {
                width: 80%;
            }
        }
    }

    /* Header Actions */
    .header_actions {
        .search_icon,
        .user_link {
            color: $color-brand;
            font-size: 16px;
            padding: 10px;
            border-radius: 50%;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;

            &:hover {
                color: $color-brand2;
                background: rgba(72, 75, 209, 0.08);
                transform: translateY(-2px);
            }

            i {
                font-size: 18px;
            }
        }

        .cart_wrapper {
            .o_wsale_my_cart {
                color: $color-brand;
                font-size: 16px;
                padding: 10px;
                border-radius: 50%;
                transition: all 0.3s ease;
                text-decoration: none;
                position: relative;

                &:hover {
                    color: $color-brand2;
                    background: rgba(72, 75, 209, 0.08);
                    transform: translateY(-2px);
                }

                .my_cart_quantity {
                    background: $color-brand2;
                    color: $color-white;
                    border-radius: 50%;
                    font-size: 11px;
                    font-weight: 600;
                    min-width: 18px;
                    height: 18px;
                    line-height: 18px;
                    text-align: center;
                    position: absolute;
                    top: 2px;
                    right: 2px;
                    animation: pulse 2s infinite;
                }
            }
        }

        .mobile_menu_toggle {
            .navbar-toggler {
                border: none;
                padding: 8px;
                background: transparent;

                &:focus {
                    box-shadow: none;
                }

                i {
                    color: $color-brand;
                    font-size: 20px;
                    transition: color 0.3s ease;
                }

                &:hover i {
                    color: $color-brand2;
                }
            }
        }
    }
}

/* Mobile Menu */
.mobile_nav_menu {
    background: $color-white;
    padding: 20px 0;
    border-top: 1px solid #eee;

    .navbar-nav {
        .nav-item {
            margin: 5px 0;

            .nav-link {
                color: $color-brand;
                font-weight: 500;
                padding: 12px 20px;
                border-radius: 8px;
                transition: all 0.3s ease;

                &:hover {
                    color: $color-brand2;
                    background: rgba(72, 75, 209, 0.08);
                    padding-left: 30px;
                }
            }
        }
    }
}

/* Search Overlay */
.search_overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: none;
    align-items: center;
    justify-content: center;

    &.active {
        display: flex;
    }

    .search_container {
        background: $color-white;
        padding: 40px;
        border-radius: 15px;
        width: 90%;
        max-width: 600px;
        position: relative;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);

        .search_form {
            display: flex;
            align-items: center;

            .search_input {
                flex: 1;
                border: 2px solid #eee;
                padding: 15px 20px;
                font-size: 16px;
                border-radius: 50px 0 0 50px;
                outline: none;
                transition: border-color 0.3s ease;

                &:focus {
                    border-color: $color-brand2;
                }
            }

            .search_submit {
                background: $color-brand2;
                color: $color-white;
                border: none;
                padding: 15px 25px;
                border-radius: 0 50px 50px 0;
                cursor: pointer;
                transition: background 0.3s ease;

                &:hover {
                    background: darken($color-brand2, 10%);
                }
            }
        }

        .search_close {
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            font-size: 20px;
            color: $color-brand;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;

            &:hover {
                background: rgba(72, 75, 209, 0.08);
                color: $color-brand2;
            }
        }
    }
}

/* Animations */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .localiza_top_nav {
        padding: 5px 0;

        .nav-pills .nav-item .nav-link {
            font-size: 12px;
            padding: 4px 12px;
        }
    }

    .localiza_main_header {
        padding: 10px 0;

        .navbar-brand .logo img {
            max-height: 35px;
        }

        .header_actions {
            .search_icon,
            .user_link {
                padding: 8px;
                font-size: 14px;

                span {
                    display: none !important;
                }
            }
        }
    }

    .search_overlay .search_container {
        padding: 20px;
        margin: 20px;

        .search_form {
            .search_input,
            .search_submit {
                padding: 12px 15px;
                font-size: 14px;
            }
        }
    }
}

/* Sticky Header Effect */
.o_header_standard.sticky {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
    animation: slideDown 0.3s ease;

    .localiza_top_nav {
        display: none;
    }

    .localiza_main_header {
        padding: 8px 0;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}