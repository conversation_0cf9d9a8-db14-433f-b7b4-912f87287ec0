/* Blue Theme Header Enhancement - #00b1be Color Scheme */

/* Blue Theme Header Styling */
.blue-theme-header {
    background: linear-gradient(135deg, $color-brand2 0%, $color-brand2-light 100%);

    .help-line {
        padding: 8px 0;

        .contact-link {
            color: $color-white;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover {
                color: rgba(255, 255, 255, 0.9);
                text-decoration: none;
                transform: translateY(-1px);
            }

            i {
                margin-right: 5px;
                font-size: 16px;
                opacity: 0.9;
            }
        }
    }
}

/* Enhanced header with #00b1be blue accents */
.o_header_standard.blue-theme-header {
    box-shadow: 0 2px 8px rgba(0, 177, 190, 0.15);
    border-bottom: 1px solid rgba(0, 177, 190, 0.1);

    .navbar-nav .nav-link {
        transition: all 0.3s ease;
        position: relative;

        &:hover {
            color: $color-brand2;
            transform: translateY(-1px);
        }

        &.active {
            color: $color-brand2;
            font-weight: 600;

            &:after {
                content: '';
                position: absolute;
                bottom: -2px;
                left: 50%;
                transform: translateX(-50%);
                width: 30px;
                height: 2px;
                background: $color-brand2;
                border-radius: 1px;
            }
        }
    }

    /* User account and search icons */
    .navbar-nav a,
    .o_header_standard a {
        &:hover {
            color: $color-brand2;
        }
    }

    /* Shopping cart styling */
    .o_wsale_my_cart {
        .blue-theme-icon {
            color: $color-brand2;
            transition: all 0.3s ease;

            &:hover {
                color: $color-brand2-hover;
                transform: scale(1.1);
            }
        }

        .my_cart_quantity {
            background: $color-brand2;
            color: $color-white;
            box-shadow: 0 2px 4px rgba(0, 177, 190, 0.3);
            animation: pulse-blue 2s infinite;
        }

        &:hover .my_cart_quantity {
            background: $color-brand2-hover;
        }
    }

    /* Search and user action buttons */
    .btn, button {
        &.btn-primary {
            background-color: $color-brand2;
            border-color: $color-brand2;

            &:hover {
                background-color: $color-brand2-hover;
                border-color: $color-brand2-hover;
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0, 177, 190, 0.3);
            }
        }
    }

    /* Form inputs focus state */
    .form-control:focus {
        border-color: $color-brand2;
        box-shadow: 0 0 0 0.2rem rgba(0, 177, 190, 0.25);
    }
}

/* Pulse animation for cart quantity */
@keyframes pulse-blue {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 4px rgba(0, 177, 190, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 4px 8px rgba(0, 177, 190, 0.4);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 4px rgba(0, 177, 190, 0.3);
    }
}

/* Additional blue theme enhancements */
.blue-theme-header {
    /* Dropdown menus */
    .dropdown-menu {
        border-color: rgba(0, 177, 190, 0.2);

        .dropdown-item {
            &:hover {
                background-color: rgba(0, 177, 190, 0.1);
                color: $color-brand2;
            }
        }
    }

    /* Links in header */
    a:not(.btn) {
        &:hover {
            color: $color-brand2;
        }
    }

    /* Mobile menu toggle */
    .navbar-toggler {
        border-color: $color-brand2;

        &:focus {
            box-shadow: 0 0 0 0.2rem rgba(0, 177, 190, 0.25);
        }

        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='%2300b1be' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .blue-theme-header .help-line {
        padding: 6px 0;

        .contact-link {
            font-size: 13px;

            i {
                font-size: 14px;
            }
        }
    }

    .o_header_standard.blue-theme-header {
        .navbar-nav .nav-link {
            padding: 8px 12px;

            &.active:after {
                width: 20px;
                height: 1px;
            }
        }
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .blue-theme-header {
        background: $color-brand2-dark;

        .contact-link {
            font-weight: 600;
        }
    }

    .o_header_standard.blue-theme-header {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            color: $color-brand2-dark;
            font-weight: 700;
        }
    }
}