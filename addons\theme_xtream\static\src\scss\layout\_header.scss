/* Simple Blue Theme Header Enhancement */

/* Blue Theme Header Styling */
.blue-theme-header {
    background: linear-gradient(135deg, $color-brand2 0%, lighten($color-brand2, 10%) 100%);

    .help-line {
        padding: 8px 0;

        .contact-link {
            color: $color-white;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover {
                color: rgba(255, 255, 255, 0.8);
                text-decoration: none;
            }

            i {
                margin-right: 5px;
                font-size: 16px;
            }
        }
    }
}

/* Enhanced header with subtle blue accents */
.o_header_standard.blue-theme-header {
    box-shadow: 0 2px 8px rgba(72, 75, 209, 0.1);

    .navbar-nav .nav-link {
        transition: all 0.3s ease;

        &:hover {
            color: $color-brand2;
        }

        &.active {
            color: $color-brand2;
            font-weight: 600;
        }
    }

    .o_wsale_my_cart {
        .blue-theme-icon {
            color: $color-brand2;
            transition: all 0.3s ease;

            &:hover {
                color: darken($color-brand2, 10%);
                transform: scale(1.1);
            }
        }

        .my_cart_quantity {
            background: $color-brand2;
            color: $color-white;
        }
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .blue-theme-header .help-line {
        padding: 6px 0;

        .contact-link {
            font-size: 13px;
        }
    }
}